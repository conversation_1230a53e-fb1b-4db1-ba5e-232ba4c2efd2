.selectionBar {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: white;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  border: 1px solid #dddee0;
  width: fit-content;
  /* 移除组件级别的动画，让容器级别的动画生效 */
}

.selectionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &:hover {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.defaultIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #4096ff;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.selectionButton {
  display: flex;
  flex: 1;
  width: 60px;
  align-items: center;
  flex-wrap: nowrap;
  margin: 0 6px;
  cursor: pointer;
  color: #1c1c1e;
  transition: all 0.2s ease;

  img {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  span {
    margin-left: 4px;
    font-size: 16px;
  }

  &:hover {
    opacity: 0.7;
  }
}

.selectionMore {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0 6px;
  cursor: pointer;
}

.selectionDots {
  font-size: 16px;
  color: #1c1c1e;

  &:hover {
    opacity: 0.7;
  }
}

.selectionDropdown {
  position: absolute;
  top: 100%;
  right: -21px;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  min-width: 120px;
  z-index: 10000;
  border: 1px solid #e5e7eb;
  margin-top: 8px;

  /* 添加下拉动画 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.95);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* 当显示时的状态 */
  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }

  /* 添加箭头指示器 */
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
  }
}

.selectionDropdownItem {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-right: 10px;
    object-fit: contain;
  }

  &:hover {
    background-color: #f0f0f0;
    color: #007bff;
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.selectionClose {
  display: flex;
  align-items: center;
  margin-right: 8px;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  color: #888;
  transition: all 0.2s ease;
}

.selectionDivider {
  width: 1px;
  height: 20px;
  background-color: #ccc;
  margin: 0 6px;
}

.selectionContainer {
  position: relative;
}

.selectionArrow {
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

.closeOptions {
  position: absolute;
  top: 100%;
  right: -150px;
  width: 181px;
  background: #f6f6f7;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  border: 1px solid #dddee0;
}

.closeOptionItem {
  padding: 5px 15px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  font-weight: 400;
  color: #1d222c;
}